# 视频模糊检测和处理工具

这个工具可以自动检测视频中的模糊失焦片段，并生成只包含清晰部分的新视频。

## 功能特点

- 自动检测视频中的模糊帧
- 支持CPU和GPU加速计算
- 生成只包含清晰片段的新视频
- 可调节的模糊检测阈值
- 详细的处理进度显示

## 文件说明

- `video_blur_detector.py` - 主程序，包含模糊检测和视频处理功能
- `test_blur_detection.py` - 测试脚本，用于验证功能和分析视频
- `Tool_verify_CUDA.py` - CUDA环境验证工具
- `actGPUenv.bat` - GPU环境激活脚本

## 使用方法

### 1. 环境准备

确保已安装必要的库：
```bash
pip install opencv-python numpy torch tqdm matplotlib
```

如果要使用GPU加速，需要安装CUDA版本的PyTorch。

### 2. 测试功能

首先运行测试脚本来分析您的视频并确定合适的阈值：

```bash
python test_blur_detection.py
```

这会分析视频的前100帧，给出统计信息和建议的模糊检测阈值。

### 3. 处理视频

使用默认设置处理视频：
```bash
python video_blur_detector.py
```

自定义参数：
```bash
python video_blur_detector.py --input 背景视频输出_测试模糊片段.mp4 --output 清晰视频输出.mp4 --threshold 80.0
```

强制使用CPU（如果GPU有问题）：
```bash
python video_blur_detector.py --cpu
```

### 4. 参数说明

- `--input`: 输入视频文件路径（默认：背景视频输出_测试模糊片段.mp4）
- `--output`: 输出视频文件路径（默认：清晰视频输出.mp4）
- `--threshold`: 模糊检测阈值，数值越低越严格（默认：100.0）
- `--cpu`: 强制使用CPU计算

## 工作原理

1. **模糊检测**: 使用拉普拉斯算子计算每帧的边缘强度方差
2. **阈值判断**: 低于阈值的帧被认为是模糊的
3. **片段提取**: 连续的清晰帧组成清晰片段
4. **视频重建**: 将所有清晰片段合并成新视频

## 阈值调节建议

- 阈值太高：可能保留一些轻微模糊的片段
- 阈值太低：可能删除一些可接受的片段
- 建议先运行测试脚本，根据统计信息调节阈值

## 注意事项

1. 处理大视频文件可能需要较长时间
2. GPU加速可以显著提高处理速度
3. 输出视频的质量取决于输入视频和阈值设置
4. 建议先用小片段测试合适的阈值

## 故障排除

如果遇到问题：

1. 检查视频文件是否存在且可读
2. 确认所需库已正确安装
3. 如果GPU有问题，使用 `--cpu` 参数
4. 调整阈值参数以获得更好的结果
