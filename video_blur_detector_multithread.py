import cv2
import numpy as np
import torch
import os
from typing import List, Tuple
import argparse
from tqdm import tqdm
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import threading
import queue
import time

class VideoBlurDetectorMultithread:
    def __init__(self, use_gpu=True, blur_threshold=100.0, num_workers=None):
        """
        初始化多线程视频模糊检测器
        
        Args:
            use_gpu: 是否使用GPU加速
            blur_threshold: 模糊阈值，低于此值认为是模糊帧
            num_workers: 工作线程数，None表示自动检测
        """
        self.blur_threshold = blur_threshold
        self.device = self._setup_device(use_gpu)
        
        # 设置工作线程数
        if num_workers is None:
            self.num_workers = min(mp.cpu_count(), 40)  # 最多使用40个核心
        else:
            self.num_workers = num_workers
            
        print(f"使用设备: {self.device}")
        print(f"CPU核心数: {mp.cpu_count()}")
        print(f"使用工作线程数: {self.num_workers}")
        
    def _setup_device(self, use_gpu):
        """设置计算设备"""
        if use_gpu and torch.cuda.is_available():
            device = torch.device('cuda')
            print(f"GPU可用: {torch.cuda.get_device_name(0)}")
        else:
            device = torch.device('cpu')
            print("使用CPU计算")
        return device
    
    def calculate_blur_score(self, frame):
        """
        计算帧的模糊分数
        使用拉普拉斯算子的方差来评估图像清晰度
        """
        # 转换为灰度图
        if len(frame.shape) == 3:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        else:
            gray = frame
            
        # 使用拉普拉斯算子计算边缘
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        
        # 计算方差作为清晰度指标
        blur_score = laplacian.var()
        
        return blur_score
    
    def process_frame_batch(self, frame_data_batch):
        """
        处理一批帧数据
        
        Args:
            frame_data_batch: 包含(frame_idx, frame)的列表
            
        Returns:
            results: 包含模糊信息的列表
        """
        results = []
        for frame_idx, frame in frame_data_batch:
            blur_score = self.calculate_blur_score(frame)
            is_blurry = blur_score < self.blur_threshold
            
            results.append({
                'frame_idx': frame_idx,
                'blur_score': blur_score,
                'is_blurry': is_blurry
            })
        
        return results
    
    def detect_blur_segments_multithread(self, video_path, batch_size=50):
        """
        使用多线程检测视频中的模糊片段
        
        Args:
            video_path: 视频文件路径
            batch_size: 每批处理的帧数
            
        Returns:
            blur_info: 包含每帧模糊信息的列表
            fps: 视频帧率
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"视频信息: {total_frames} 帧, {fps:.2f} FPS")
        print(f"批处理大小: {batch_size} 帧/批")
        
        # 读取所有帧到内存（如果内存足够）
        print("正在读取视频帧到内存...")
        frames = []
        frame_indices = []
        
        with tqdm(total=total_frames, desc="读取帧") as pbar:
            frame_idx = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frames.append(frame.copy())
                frame_indices.append(frame_idx)
                frame_idx += 1
                pbar.update(1)
        
        cap.release()
        
        # 创建批次
        batches = []
        for i in range(0, len(frames), batch_size):
            batch_frames = frames[i:i+batch_size]
            batch_indices = frame_indices[i:i+batch_size]
            batch = list(zip(batch_indices, batch_frames))
            batches.append(batch)
        
        print(f"创建了 {len(batches)} 个批次")
        
        # 使用线程池处理批次
        all_results = []
        
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            # 提交所有批次任务
            future_to_batch = {
                executor.submit(self.process_frame_batch, batch): i 
                for i, batch in enumerate(batches)
            }
            
            # 收集结果
            with tqdm(total=len(batches), desc="处理批次") as pbar:
                for future in future_to_batch:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    pbar.update(1)
        
        # 按帧索引排序
        all_results.sort(key=lambda x: x['frame_idx'])
        
        # 添加时间戳
        for result in all_results:
            result['timestamp'] = result['frame_idx'] / fps
        
        return all_results, fps
    
    def detect_blur_segments_memory_efficient(self, video_path, batch_size=10):
        """
        内存高效的多线程检测方法
        适用于大视频文件或内存有限的情况
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"视频信息: {total_frames} 帧, {fps:.2f} FPS")
        print(f"使用内存高效模式，批处理大小: {batch_size}")
        
        # 使用队列进行生产者-消费者模式
        frame_queue = queue.Queue(maxsize=self.num_workers * 2)
        result_queue = queue.Queue()
        
        def frame_reader():
            """读取帧的生产者线程"""
            frame_idx = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frame_queue.put((frame_idx, frame.copy()))
                frame_idx += 1
            
            # 发送结束信号
            for _ in range(self.num_workers):
                frame_queue.put(None)
            cap.release()
        
        def frame_processor(worker_id):
            """处理帧的消费者线程"""
            while True:
                item = frame_queue.get()
                if item is None:
                    break
                
                frame_idx, frame = item
                blur_score = self.calculate_blur_score(frame)
                is_blurry = blur_score < self.blur_threshold
                timestamp = frame_idx / fps
                
                result_queue.put({
                    'frame_idx': frame_idx,
                    'timestamp': timestamp,
                    'blur_score': blur_score,
                    'is_blurry': is_blurry
                })
                frame_queue.task_done()
        
        # 启动线程
        reader_thread = threading.Thread(target=frame_reader)
        reader_thread.start()
        
        processor_threads = []
        for i in range(self.num_workers):
            thread = threading.Thread(target=frame_processor, args=(i,))
            thread.start()
            processor_threads.append(thread)
        
        # 收集结果
        results = []
        with tqdm(total=total_frames, desc="处理帧") as pbar:
            processed_count = 0
            while processed_count < total_frames:
                try:
                    result = result_queue.get(timeout=1)
                    results.append(result)
                    processed_count += 1
                    pbar.update(1)
                except queue.Empty:
                    continue
        
        # 等待所有线程完成
        reader_thread.join()
        for thread in processor_threads:
            thread.join()
        
        # 按帧索引排序
        results.sort(key=lambda x: x['frame_idx'])
        
        return results, fps
    
    def find_clear_segments(self, blur_info):
        """找到清晰的视频片段"""
        clear_segments = []
        current_start = None
        
        for info in blur_info:
            if not info['is_blurry']:  # 清晰帧
                if current_start is None:
                    current_start = info['timestamp']
            else:  # 模糊帧
                if current_start is not None:
                    # 结束当前清晰片段
                    clear_segments.append((current_start, info['timestamp']))
                    current_start = None
        
        # 处理最后一个片段
        if current_start is not None:
            clear_segments.append((current_start, blur_info[-1]['timestamp']))
        
        return clear_segments
    
    def create_clear_video(self, input_path, output_path, clear_segments):
        """创建只包含清晰片段的新视频"""
        cap = cv2.VideoCapture(input_path)
        
        # 获取视频属性
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        print(f"创建清晰视频: {len(clear_segments)} 个片段")
        
        for start_time, end_time in tqdm(clear_segments, desc="处理片段"):
            # 跳转到片段开始
            cap.set(cv2.CAP_PROP_POS_MSEC, start_time * 1000)
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                current_time = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0
                
                if current_time > end_time:
                    break
                
                out.write(frame)
        
        cap.release()
        out.release()
        print(f"清晰视频已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='多线程视频模糊检测和处理')
    parser.add_argument('--input', default='背景视频输出_测试模糊片段.mp4', 
                       help='输入视频文件路径')
    parser.add_argument('--output', default='清晰视频输出_多线程.mp4', 
                       help='输出视频文件路径')
    parser.add_argument('--threshold', type=float, default=100.0, 
                       help='模糊阈值')
    parser.add_argument('--cpu', action='store_true', 
                       help='强制使用CPU')
    parser.add_argument('--workers', type=int, default=None,
                       help='工作线程数 (默认: 自动检测)')
    parser.add_argument('--memory-efficient', action='store_true',
                       help='使用内存高效模式')
    parser.add_argument('--batch-size', type=int, default=50,
                       help='批处理大小')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 找不到输入文件 {args.input}")
        return
    
    # 创建检测器
    detector = VideoBlurDetectorMultithread(
        use_gpu=not args.cpu, 
        blur_threshold=args.threshold,
        num_workers=args.workers
    )
    
    # 检测模糊片段
    print("开始检测模糊片段...")
    start_time = time.time()
    
    if args.memory_efficient:
        blur_info, fps = detector.detect_blur_segments_memory_efficient(
            args.input, batch_size=args.batch_size
        )
    else:
        blur_info, fps = detector.detect_blur_segments_multithread(
            args.input, batch_size=args.batch_size
        )
    
    processing_time = time.time() - start_time
    
    # 分析结果
    total_frames = len(blur_info)
    blurry_frames = sum(1 for info in blur_info if info['is_blurry'])
    clear_frames = total_frames - blurry_frames
    
    print(f"\n检测结果:")
    print(f"总帧数: {total_frames}")
    print(f"模糊帧数: {blurry_frames} ({blurry_frames/total_frames*100:.1f}%)")
    print(f"清晰帧数: {clear_frames} ({clear_frames/total_frames*100:.1f}%)")
    print(f"处理时间: {processing_time:.2f} 秒")
    print(f"处理速度: {total_frames/processing_time:.1f} 帧/秒")
    
    # 找到清晰片段
    clear_segments = detector.find_clear_segments(blur_info)
    
    if clear_segments:
        total_clear_duration = sum(end - start for start, end in clear_segments)
        print(f"\n清晰片段: {len(clear_segments)} 个")
        print(f"清晰片段总时长: {total_clear_duration:.2f} 秒")
        
        for i, (start, end) in enumerate(clear_segments):
            print(f"  片段 {i+1}: {start:.2f}s - {end:.2f}s (时长: {end-start:.2f}s)")
        
        # 创建清晰视频
        detector.create_clear_video(args.input, args.output, clear_segments)
    else:
        print("警告: 没有找到清晰的片段!")

if __name__ == "__main__":
    main()
