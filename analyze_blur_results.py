import cv2
import numpy as np
import matplotlib.pyplot as plt
from video_blur_detector import VideoBlurDetector
import os
import argparse

def analyze_video_blur_distribution(video_path, sample_frames=500):
    """
    分析视频的模糊分布情况
    
    Args:
        video_path: 视频文件路径
        sample_frames: 采样帧数
    """
    if not os.path.exists(video_path):
        print(f"找不到视频文件: {video_path}")
        return
    
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"视频信息: {total_frames} 帧, {fps:.2f} FPS, 总时长: {total_frames/fps:.2f}秒")
    
    # 计算采样间隔
    if sample_frames >= total_frames:
        sample_interval = 1
        sample_frames = total_frames
    else:
        sample_interval = total_frames // sample_frames
    
    detector = VideoBlurDetector(use_gpu=False, blur_threshold=100.0)
    
    scores = []
    timestamps = []
    frame_indices = []
    
    print(f"采样分析 {sample_frames} 帧 (间隔: {sample_interval})...")
    
    for i in range(0, total_frames, sample_interval):
        cap.set(cv2.CAP_PROP_POS_FRAMES, i)
        ret, frame = cap.read()
        
        if not ret:
            break
            
        score = detector.calculate_blur_score(frame)
        timestamp = i / fps
        
        scores.append(score)
        timestamps.append(timestamp)
        frame_indices.append(i)
        
        if len(scores) % 50 == 0:
            print(f"已处理 {len(scores)} 帧...")
    
    cap.release()
    
    # 统计分析
    scores = np.array(scores)
    timestamps = np.array(timestamps)
    
    print(f"\n=== 模糊分数统计 ===")
    print(f"平均值: {np.mean(scores):.2f}")
    print(f"中位数: {np.median(scores):.2f}")
    print(f"标准差: {np.std(scores):.2f}")
    print(f"最小值: {np.min(scores):.2f}")
    print(f"最大值: {np.max(scores):.2f}")
    print(f"25%分位数: {np.percentile(scores, 25):.2f}")
    print(f"75%分位数: {np.percentile(scores, 75):.2f}")
    
    # 建议不同的阈值
    thresholds = [
        np.percentile(scores, 10),  # 最严格：只保留最清晰的10%
        np.percentile(scores, 25),  # 严格：保留最清晰的25%
        np.percentile(scores, 50),  # 中等：保留最清晰的50%
        np.percentile(scores, 75),  # 宽松：保留最清晰的75%
    ]
    
    print(f"\n=== 建议阈值 ===")
    for i, threshold in enumerate(thresholds):
        clear_ratio = np.sum(scores >= threshold) / len(scores) * 100
        print(f"阈值 {threshold:.1f}: 保留 {clear_ratio:.1f}% 的帧")
    
    # 绘制分析图
    plt.figure(figsize=(15, 10))
    
    # 子图1: 时间序列
    plt.subplot(2, 2, 1)
    plt.plot(timestamps, scores, 'b-', alpha=0.7, linewidth=1)
    plt.axhline(y=np.mean(scores), color='r', linestyle='--', label=f'平均值: {np.mean(scores):.1f}')
    plt.axhline(y=np.median(scores), color='g', linestyle='--', label=f'中位数: {np.median(scores):.1f}')
    plt.xlabel('时间 (秒)')
    plt.ylabel('模糊分数')
    plt.title('模糊分数时间序列')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2: 直方图
    plt.subplot(2, 2, 2)
    plt.hist(scores, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(x=np.mean(scores), color='r', linestyle='--', label=f'平均值: {np.mean(scores):.1f}')
    plt.axvline(x=np.median(scores), color='g', linestyle='--', label=f'中位数: {np.median(scores):.1f}')
    plt.xlabel('模糊分数')
    plt.ylabel('频次')
    plt.title('模糊分数分布')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图3: 不同阈值下的清晰片段比例
    plt.subplot(2, 2, 3)
    test_thresholds = np.linspace(np.min(scores), np.max(scores), 100)
    clear_ratios = [np.sum(scores >= t) / len(scores) * 100 for t in test_thresholds]
    plt.plot(test_thresholds, clear_ratios, 'b-', linewidth=2)
    plt.xlabel('阈值')
    plt.ylabel('清晰帧比例 (%)')
    plt.title('阈值 vs 清晰帧比例')
    plt.grid(True, alpha=0.3)
    
    # 标记建议阈值点
    for threshold in thresholds:
        clear_ratio = np.sum(scores >= threshold) / len(scores) * 100
        plt.plot(threshold, clear_ratio, 'ro', markersize=8)
        plt.annotate(f'{threshold:.1f}', (threshold, clear_ratio), 
                    xytext=(5, 5), textcoords='offset points')
    
    # 子图4: 滑动窗口平均
    plt.subplot(2, 2, 4)
    window_size = max(1, len(scores) // 50)  # 窗口大小为总帧数的1/50
    smoothed_scores = np.convolve(scores, np.ones(window_size)/window_size, mode='valid')
    smoothed_timestamps = timestamps[:len(smoothed_scores)]
    
    plt.plot(smoothed_timestamps, smoothed_scores, 'b-', linewidth=2, label=f'滑动平均 (窗口={window_size})')
    plt.axhline(y=np.mean(scores), color='r', linestyle='--', label=f'总体平均: {np.mean(scores):.1f}')
    plt.xlabel('时间 (秒)')
    plt.ylabel('模糊分数')
    plt.title('平滑后的模糊分数')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('blur_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n分析图表已保存为: blur_analysis.png")
    
    return scores, timestamps, thresholds

def compare_thresholds(video_path, thresholds):
    """
    比较不同阈值的效果
    """
    print(f"\n=== 比较不同阈值效果 ===")
    
    for threshold in thresholds:
        detector = VideoBlurDetector(use_gpu=False, blur_threshold=threshold)
        blur_info, fps = detector.detect_blur_segments(video_path)
        
        total_frames = len(blur_info)
        blurry_frames = sum(1 for info in blur_info if info['is_blurry'])
        clear_frames = total_frames - blurry_frames
        
        clear_segments = detector.find_clear_segments(blur_info)
        total_clear_duration = sum(end - start for start, end in clear_segments) if clear_segments else 0
        
        print(f"\n阈值 {threshold:.1f}:")
        print(f"  清晰帧: {clear_frames}/{total_frames} ({clear_frames/total_frames*100:.1f}%)")
        print(f"  清晰片段: {len(clear_segments)} 个")
        print(f"  清晰总时长: {total_clear_duration:.2f} 秒")

def main():
    parser = argparse.ArgumentParser(description='视频模糊分析工具')
    parser.add_argument('--input', default='背景视频输出_测试模糊片段.mp4', 
                       help='输入视频文件路径')
    parser.add_argument('--samples', type=int, default=500, 
                       help='采样帧数')
    parser.add_argument('--compare', action='store_true', 
                       help='比较不同阈值效果')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 找不到输入文件 {args.input}")
        return
    
    # 分析模糊分布
    scores, timestamps, suggested_thresholds = analyze_video_blur_distribution(args.input, args.samples)
    
    # 比较阈值效果
    if args.compare:
        compare_thresholds(args.input, suggested_thresholds)

if __name__ == "__main__":
    main()
