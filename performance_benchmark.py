import time
import psutil
import threading
import matplotlib.pyplot as plt
import numpy as np
from video_blur_detector import VideoBlurDetector
from video_blur_detector_multithread import VideoBlurDetectorMultithread
import argparse
import os

class PerformanceMonitor:
    def __init__(self):
        self.cpu_usage = []
        self.memory_usage = []
        self.timestamps = []
        self.monitoring = False
        
    def start_monitoring(self):
        """开始监控系统资源"""
        self.monitoring = True
        self.cpu_usage = []
        self.memory_usage = []
        self.timestamps = []
        
        def monitor():
            start_time = time.time()
            while self.monitoring:
                current_time = time.time() - start_time
                cpu_percent = psutil.cpu_percent(interval=0.1)
                memory_percent = psutil.virtual_memory().percent
                
                self.timestamps.append(current_time)
                self.cpu_usage.append(cpu_percent)
                self.memory_usage.append(memory_percent)
                
                time.sleep(0.5)
        
        self.monitor_thread = threading.Thread(target=monitor)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join()
    
    def get_stats(self):
        """获取统计信息"""
        if not self.cpu_usage:
            return {}
        
        return {
            'avg_cpu': np.mean(self.cpu_usage),
            'max_cpu': np.max(self.cpu_usage),
            'avg_memory': np.mean(self.memory_usage),
            'max_memory': np.max(self.memory_usage),
            'duration': self.timestamps[-1] if self.timestamps else 0
        }

def benchmark_single_thread(video_path, threshold=100.0):
    """测试单线程性能"""
    print("=== 单线程性能测试 ===")
    
    monitor = PerformanceMonitor()
    monitor.start_monitoring()
    
    detector = VideoBlurDetector(use_gpu=False, blur_threshold=threshold)
    
    start_time = time.time()
    blur_info, fps = detector.detect_blur_segments(video_path)
    end_time = time.time()
    
    monitor.stop_monitoring()
    
    processing_time = end_time - start_time
    total_frames = len(blur_info)
    
    stats = monitor.get_stats()
    
    result = {
        'method': 'Single Thread',
        'processing_time': processing_time,
        'total_frames': total_frames,
        'fps': total_frames / processing_time,
        'cpu_usage': stats.get('avg_cpu', 0),
        'max_cpu': stats.get('max_cpu', 0),
        'memory_usage': stats.get('avg_memory', 0),
        'monitor_data': {
            'timestamps': monitor.timestamps,
            'cpu_usage': monitor.cpu_usage,
            'memory_usage': monitor.memory_usage
        }
    }
    
    print(f"处理时间: {processing_time:.2f} 秒")
    print(f"处理速度: {result['fps']:.1f} 帧/秒")
    print(f"平均CPU使用率: {result['cpu_usage']:.1f}%")
    print(f"最大CPU使用率: {result['max_cpu']:.1f}%")
    print(f"平均内存使用率: {result['memory_usage']:.1f}%")
    
    return result, blur_info

def benchmark_multithread(video_path, threshold=100.0, num_workers=None, memory_efficient=False):
    """测试多线程性能"""
    mode = "内存高效" if memory_efficient else "标准"
    workers = num_workers or "自动"
    print(f"=== 多线程性能测试 ({mode}模式, {workers}线程) ===")
    
    monitor = PerformanceMonitor()
    monitor.start_monitoring()
    
    detector = VideoBlurDetectorMultithread(
        use_gpu=False, 
        blur_threshold=threshold,
        num_workers=num_workers
    )
    
    start_time = time.time()
    if memory_efficient:
        blur_info, fps = detector.detect_blur_segments_memory_efficient(video_path)
    else:
        blur_info, fps = detector.detect_blur_segments_multithread(video_path)
    end_time = time.time()
    
    monitor.stop_monitoring()
    
    processing_time = end_time - start_time
    total_frames = len(blur_info)
    
    stats = monitor.get_stats()
    
    result = {
        'method': f'Multi Thread ({workers} workers, {mode})',
        'processing_time': processing_time,
        'total_frames': total_frames,
        'fps': total_frames / processing_time,
        'cpu_usage': stats.get('avg_cpu', 0),
        'max_cpu': stats.get('max_cpu', 0),
        'memory_usage': stats.get('avg_memory', 0),
        'num_workers': detector.num_workers,
        'monitor_data': {
            'timestamps': monitor.timestamps,
            'cpu_usage': monitor.cpu_usage,
            'memory_usage': monitor.memory_usage
        }
    }
    
    print(f"处理时间: {processing_time:.2f} 秒")
    print(f"处理速度: {result['fps']:.1f} 帧/秒")
    print(f"平均CPU使用率: {result['cpu_usage']:.1f}%")
    print(f"最大CPU使用率: {result['max_cpu']:.1f}%")
    print(f"平均内存使用率: {result['memory_usage']:.1f}%")
    print(f"实际使用线程数: {result['num_workers']}")
    
    return result, blur_info

def plot_performance_comparison(results):
    """绘制性能对比图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    methods = [r['method'] for r in results]
    processing_times = [r['processing_time'] for r in results]
    fps_values = [r['fps'] for r in results]
    cpu_usage = [r['cpu_usage'] for r in results]
    memory_usage = [r['memory_usage'] for r in results]
    
    # 处理时间对比
    ax1.bar(methods, processing_times, color=['blue', 'green', 'orange', 'red'][:len(methods)])
    ax1.set_title('Processing Time Comparison')
    ax1.set_ylabel('Time (seconds)')
    ax1.tick_params(axis='x', rotation=45)
    
    # 处理速度对比
    ax2.bar(methods, fps_values, color=['blue', 'green', 'orange', 'red'][:len(methods)])
    ax2.set_title('Processing Speed Comparison')
    ax2.set_ylabel('Frames per Second')
    ax2.tick_params(axis='x', rotation=45)
    
    # CPU使用率对比
    ax3.bar(methods, cpu_usage, color=['blue', 'green', 'orange', 'red'][:len(methods)])
    ax3.set_title('Average CPU Usage')
    ax3.set_ylabel('CPU Usage (%)')
    ax3.tick_params(axis='x', rotation=45)
    
    # 内存使用率对比
    ax4.bar(methods, memory_usage, color=['blue', 'green', 'orange', 'red'][:len(methods)])
    ax4.set_title('Average Memory Usage')
    ax4.set_ylabel('Memory Usage (%)')
    ax4.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_resource_usage_timeline(results):
    """绘制资源使用时间线"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8))
    
    colors = ['blue', 'green', 'orange', 'red']
    
    for i, result in enumerate(results):
        monitor_data = result['monitor_data']
        if monitor_data['timestamps']:
            ax1.plot(monitor_data['timestamps'], monitor_data['cpu_usage'], 
                    color=colors[i % len(colors)], label=result['method'], linewidth=2)
            ax2.plot(monitor_data['timestamps'], monitor_data['memory_usage'], 
                    color=colors[i % len(colors)], label=result['method'], linewidth=2)
    
    ax1.set_title('CPU Usage Over Time')
    ax1.set_ylabel('CPU Usage (%)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    ax2.set_title('Memory Usage Over Time')
    ax2.set_xlabel('Time (seconds)')
    ax2.set_ylabel('Memory Usage (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('resource_usage_timeline.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='视频处理性能基准测试')
    parser.add_argument('--input', default='背景视频输出_测试模糊片段.mp4', 
                       help='输入视频文件路径')
    parser.add_argument('--threshold', type=float, default=179.0, 
                       help='模糊阈值')
    parser.add_argument('--max-workers', type=int, default=None,
                       help='最大工作线程数')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 找不到输入文件 {args.input}")
        return
    
    print("开始性能基准测试...")
    print(f"CPU核心数: {psutil.cpu_count()}")
    print(f"物理CPU核心数: {psutil.cpu_count(logical=False)}")
    print(f"总内存: {psutil.virtual_memory().total / (1024**3):.1f} GB")
    print(f"可用内存: {psutil.virtual_memory().available / (1024**3):.1f} GB")
    print()
    
    results = []
    
    # 测试1: 单线程
    try:
        result1, blur_info1 = benchmark_single_thread(args.input, args.threshold)
        results.append(result1)
        print()
    except Exception as e:
        print(f"单线程测试失败: {e}")
        print()
    
    # 测试2: 多线程标准模式
    try:
        result2, blur_info2 = benchmark_multithread(args.input, args.threshold, args.max_workers, False)
        results.append(result2)
        print()
    except Exception as e:
        print(f"多线程标准模式测试失败: {e}")
        print()
    
    # 测试3: 多线程内存高效模式
    try:
        result3, blur_info3 = benchmark_multithread(args.input, args.threshold, args.max_workers, True)
        results.append(result3)
        print()
    except Exception as e:
        print(f"多线程内存高效模式测试失败: {e}")
        print()
    
    # 生成报告
    if results:
        print("=== 性能对比总结 ===")
        for result in results:
            speedup = results[0]['processing_time'] / result['processing_time'] if results else 1.0
            print(f"{result['method']}:")
            print(f"  处理时间: {result['processing_time']:.2f}s")
            print(f"  处理速度: {result['fps']:.1f} 帧/秒")
            print(f"  加速比: {speedup:.2f}x")
            print(f"  CPU使用率: {result['cpu_usage']:.1f}%")
            print()
        
        # 绘制对比图
        plot_performance_comparison(results)
        plot_resource_usage_timeline(results)
        
        print("性能对比图已保存:")
        print("- performance_comparison.png")
        print("- resource_usage_timeline.png")

if __name__ == "__main__":
    main()
