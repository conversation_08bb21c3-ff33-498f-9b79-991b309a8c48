import cv2
import numpy as np
import matplotlib.pyplot as plt
from video_blur_detector import VideoBlurDetector
import os

def test_single_frame_blur():
    """测试单帧模糊检测"""
    print("测试单帧模糊检测...")
    
    # 创建测试图像
    # 清晰图像
    clear_img = np.zeros((100, 100), dtype=np.uint8)
    cv2.rectangle(clear_img, (20, 20), (80, 80), 255, 2)
    cv2.line(clear_img, (30, 30), (70, 70), 128, 1)
    
    # 模糊图像
    blurry_img = cv2.GaussianBlur(clear_img, (15, 15), 5)
    
    detector = VideoBlurDetector(use_gpu=False, blur_threshold=100.0)
    
    clear_score = detector.calculate_blur_score(clear_img)
    blurry_score = detector.calculate_blur_score(blurry_img)
    
    print(f"清晰图像模糊分数: {clear_score:.2f}")
    print(f"模糊图像模糊分数: {blurry_score:.2f}")
    print(f"阈值: {detector.blur_threshold}")
    
    return clear_score > blurry_score

def analyze_video_sample():
    """分析视频样本的前几帧"""
    video_path = "背景视频输出_测试模糊片段.mp4"
    
    if not os.path.exists(video_path):
        print(f"找不到视频文件: {video_path}")
        return
    
    cap = cv2.VideoCapture(video_path)
    detector = VideoBlurDetector(use_gpu=False, blur_threshold=100.0)
    
    scores = []
    frame_count = 0
    max_frames = 100  # 只分析前100帧
    
    print(f"分析视频前{max_frames}帧...")
    
    while frame_count < max_frames:
        ret, frame = cap.read()
        if not ret:
            break
            
        score = detector.calculate_blur_score(frame)
        scores.append(score)
        
        if frame_count % 10 == 0:
            print(f"帧 {frame_count}: 模糊分数 = {score:.2f}")
        
        frame_count += 1
    
    cap.release()
    
    if scores:
        avg_score = np.mean(scores)
        min_score = np.min(scores)
        max_score = np.max(scores)
        std_score = np.std(scores)
        
        print(f"\n统计信息 (前{len(scores)}帧):")
        print(f"平均模糊分数: {avg_score:.2f}")
        print(f"最小模糊分数: {min_score:.2f}")
        print(f"最大模糊分数: {max_score:.2f}")
        print(f"标准差: {std_score:.2f}")
        
        # 建议阈值
        suggested_threshold = avg_score * 0.6  # 低于平均值60%认为是模糊
        print(f"建议阈值: {suggested_threshold:.2f}")
        
        # 统计模糊帧数量
        blurry_count = sum(1 for score in scores if score < suggested_threshold)
        print(f"使用建议阈值，模糊帧数量: {blurry_count}/{len(scores)} ({blurry_count/len(scores)*100:.1f}%)")

def main():
    print("=== 视频模糊检测测试 ===\n")
    
    # 测试1: 单帧检测
    if test_single_frame_blur():
        print("✓ 单帧模糊检测测试通过\n")
    else:
        print("✗ 单帧模糊检测测试失败\n")
    
    # 测试2: 视频样本分析
    analyze_video_sample()

if __name__ == "__main__":
    main()
