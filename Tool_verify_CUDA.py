import torch
import cv2

# 打印基本版本信息
print(f"OpenCV版本: {cv2.__version__}")
print("PyTorch 版本:", torch.__version__)
print("torch 路径:", torch.__file__)

# 检查CUDA是否可用
cuda_available = torch.cuda.is_available()
print("CUDA 是否可用:", cuda_available)

# 只有CUDA可用时才执行以下代码
if cuda_available:
    print("可用的GPU数量:", torch.cuda.device_count())
    
    # 打印所有可用GPU的编号和名称
    for gpu_id in range(torch.cuda.device_count()):
        print(f"GPU {gpu_id} 名称: {torch.cuda.get_device_name(gpu_id)}")
    
    # 打印当前使用的GPU设备
    print("当前设备的索引:", torch.cuda.current_device())
else:
    print("CUDA不可用，无法获取GPU信息")