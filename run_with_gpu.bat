@echo off
echo ========================================
echo 视频模糊检测和处理工具 (GPU版本)
echo ========================================

:: 保存当前目录
set "originalDir=%cd%"

:: 激活GPU环境
echo 正在激活GPU环境...
cd /d "C:\Users\<USER>\Downloads\code-program\YOLOv10\PYenv-GPUtrain\Scripts"
call activate.bat

:: 返回到工作目录
cd /d "%originalDir%"

:: 验证CUDA环境
echo.
echo 验证CUDA环境:
python Tool_verify_CUDA.py

echo.
echo ========================================
echo 选择要执行的操作:
echo 1. 分析视频模糊分布
echo 2. 处理视频 (阈值=179, 保留75%清晰帧)
echo 3. 处理视频 (阈值=186, 保留50%清晰帧)
echo 4. 处理视频 (阈值=189, 保留25%清晰帧)
echo 5. 自定义阈值处理
echo ========================================

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" (
    echo 正在分析视频模糊分布...
    python analyze_blur_results.py --samples 300
) else if "%choice%"=="2" (
    echo 正在处理视频 (阈值=179)...
    python video_blur_detector.py --threshold 179 --output 清晰视频_75percent.mp4
) else if "%choice%"=="3" (
    echo 正在处理视频 (阈值=186)...
    python video_blur_detector.py --threshold 186 --output 清晰视频_50percent.mp4
) else if "%choice%"=="4" (
    echo 正在处理视频 (阈值=189)...
    python video_blur_detector.py --threshold 189 --output 清晰视频_25percent.mp4
) else if "%choice%"=="5" (
    set /p threshold="请输入阈值 (建议范围: 100-200): "
    set /p output="请输入输出文件名 (例如: 自定义输出.mp4): "
    echo 正在处理视频 (阈值=!threshold!)...
    python video_blur_detector.py --threshold !threshold! --output "!output!"
) else (
    echo 无效选择，退出程序。
    pause
    exit /b 1
)

echo.
echo ========================================
echo 处理完成！
echo ========================================
pause
