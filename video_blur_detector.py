import cv2
import numpy as np
import torch
import os
from typing import List, Tuple
import argparse
from tqdm import tqdm

class VideoBlurDetector:
    def __init__(self, use_gpu=True, blur_threshold=100.0):
        """
        初始化视频模糊检测器
        
        Args:
            use_gpu: 是否使用GPU加速
            blur_threshold: 模糊阈值，低于此值认为是模糊帧
        """
        self.blur_threshold = blur_threshold
        self.device = self._setup_device(use_gpu)
        print(f"使用设备: {self.device}")
        
    def _setup_device(self, use_gpu):
        """设置计算设备"""
        if use_gpu and torch.cuda.is_available():
            device = torch.device('cuda')
            print(f"GPU可用: {torch.cuda.get_device_name(0)}")
        else:
            device = torch.device('cpu')
            print("使用CPU计算")
        return device
    
    def calculate_blur_score(self, frame):
        """
        计算帧的模糊分数
        使用拉普拉斯算子的方差来评估图像清晰度
        
        Args:
            frame: 输入帧
            
        Returns:
            blur_score: 模糊分数，值越高越清晰
        """
        # 转换为灰度图
        if len(frame.shape) == 3:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        else:
            gray = frame
            
        # 使用拉普拉斯算子计算边缘
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        
        # 计算方差作为清晰度指标
        blur_score = laplacian.var()
        
        return blur_score
    
    def calculate_blur_score_gpu(self, frame):
        """
        GPU版本的模糊分数计算
        """
        try:
            # 转换为灰度图
            if len(frame.shape) == 3:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = frame
            
            # 转换为torch tensor并移到GPU
            gray_tensor = torch.from_numpy(gray).float().to(self.device)
            
            # 定义拉普拉斯核
            laplacian_kernel = torch.tensor([
                [0, 1, 0],
                [1, -4, 1],
                [0, 1, 0]
            ], dtype=torch.float32).to(self.device)
            
            # 添加batch和channel维度
            gray_tensor = gray_tensor.unsqueeze(0).unsqueeze(0)
            laplacian_kernel = laplacian_kernel.unsqueeze(0).unsqueeze(0)
            
            # 应用卷积
            laplacian_result = torch.nn.functional.conv2d(
                gray_tensor, laplacian_kernel, padding=1
            )
            
            # 计算方差
            blur_score = torch.var(laplacian_result).item()
            
            return blur_score
            
        except Exception as e:
            print(f"GPU计算失败，回退到CPU: {e}")
            return self.calculate_blur_score(frame)
    
    def detect_blur_segments(self, video_path):
        """
        检测视频中的模糊片段
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            blur_info: 包含每帧模糊信息的列表
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"视频信息: {total_frames} 帧, {fps:.2f} FPS")
        
        blur_info = []
        
        with tqdm(total=total_frames, desc="检测模糊帧") as pbar:
            frame_idx = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 计算模糊分数
                if self.device.type == 'cuda':
                    blur_score = self.calculate_blur_score_gpu(frame)
                else:
                    blur_score = self.calculate_blur_score(frame)
                
                is_blurry = blur_score < self.blur_threshold
                timestamp = frame_idx / fps
                
                blur_info.append({
                    'frame_idx': frame_idx,
                    'timestamp': timestamp,
                    'blur_score': blur_score,
                    'is_blurry': is_blurry
                })
                
                frame_idx += 1
                pbar.update(1)
        
        cap.release()
        return blur_info, fps
    
    def find_clear_segments(self, blur_info):
        """
        找到清晰的视频片段
        
        Args:
            blur_info: 模糊检测信息
            
        Returns:
            clear_segments: 清晰片段的时间范围列表
        """
        clear_segments = []
        current_start = None
        
        for info in blur_info:
            if not info['is_blurry']:  # 清晰帧
                if current_start is None:
                    current_start = info['timestamp']
            else:  # 模糊帧
                if current_start is not None:
                    # 结束当前清晰片段
                    clear_segments.append((current_start, info['timestamp']))
                    current_start = None
        
        # 处理最后一个片段
        if current_start is not None:
            clear_segments.append((current_start, blur_info[-1]['timestamp']))
        
        return clear_segments
    
    def create_clear_video(self, input_path, output_path, clear_segments):
        """
        创建只包含清晰片段的新视频
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            clear_segments: 清晰片段时间范围
        """
        cap = cv2.VideoCapture(input_path)
        
        # 获取视频属性
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        print(f"创建清晰视频: {len(clear_segments)} 个片段")
        
        for start_time, end_time in tqdm(clear_segments, desc="处理片段"):
            # 跳转到片段开始
            cap.set(cv2.CAP_PROP_POS_MSEC, start_time * 1000)
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                current_time = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0
                
                if current_time > end_time:
                    break
                
                out.write(frame)
        
        cap.release()
        out.release()
        print(f"清晰视频已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='视频模糊检测和处理')
    parser.add_argument('--input', default='背景视频输出_测试模糊片段.mp4', 
                       help='输入视频文件路径')
    parser.add_argument('--output', default='清晰视频输出.mp4', 
                       help='输出视频文件路径')
    parser.add_argument('--threshold', type=float, default=100.0, 
                       help='模糊阈值')
    parser.add_argument('--cpu', action='store_true', 
                       help='强制使用CPU')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 找不到输入文件 {args.input}")
        return
    
    # 创建检测器
    detector = VideoBlurDetector(use_gpu=not args.cpu, blur_threshold=args.threshold)
    
    # 检测模糊片段
    print("开始检测模糊片段...")
    blur_info, fps = detector.detect_blur_segments(args.input)
    
    # 分析结果
    total_frames = len(blur_info)
    blurry_frames = sum(1 for info in blur_info if info['is_blurry'])
    clear_frames = total_frames - blurry_frames
    
    print(f"\n检测结果:")
    print(f"总帧数: {total_frames}")
    print(f"模糊帧数: {blurry_frames} ({blurry_frames/total_frames*100:.1f}%)")
    print(f"清晰帧数: {clear_frames} ({clear_frames/total_frames*100:.1f}%)")
    
    # 找到清晰片段
    clear_segments = detector.find_clear_segments(blur_info)
    
    if clear_segments:
        total_clear_duration = sum(end - start for start, end in clear_segments)
        print(f"\n清晰片段: {len(clear_segments)} 个")
        print(f"清晰片段总时长: {total_clear_duration:.2f} 秒")
        
        for i, (start, end) in enumerate(clear_segments):
            print(f"  片段 {i+1}: {start:.2f}s - {end:.2f}s (时长: {end-start:.2f}s)")
        
        # 创建清晰视频
        detector.create_clear_video(args.input, args.output, clear_segments)
    else:
        print("警告: 没有找到清晰的片段!")

if __name__ == "__main__":
    main()
